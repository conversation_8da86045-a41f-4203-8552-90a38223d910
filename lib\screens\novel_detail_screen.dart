import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/services/content_review_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:novel_app/controllers/novel_controller.dart';

class NovelDetailScreen extends StatefulWidget {
  final Novel novel;

  const NovelDetailScreen({super.key, required this.novel});

  @override
  State<NovelDetailScreen> createState() => _NovelDetailScreenState();
}

class _NovelDetailScreenState extends State<NovelDetailScreen> {
  final NovelController _controller = Get.find<NovelController>();
  final TextEditingController _titleController = TextEditingController();
  final List<TextEditingController> _chapterControllers = [];
  final List<String> _undoHistory = [];
  final List<String> _redoHistory = [];
  bool _isEditing = false;
  bool _isMarkdownMode = false; // 新增：控制markdown渲染模式
  int _currentChapterIndex = 0;
  late Novel _currentNovel;
  TextSelection? _lastSelection;
  final _aiService = Get.find<AIService>();

  // 大纲查看器相关状态
  int _selectedOutlineSectionIndex = 0;
  final ScrollController _outlineScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel.copyWith();
    _titleController.text = _currentNovel.title;
    _initChapterControllers();
  }

  void _initChapterControllers() {
    _chapterControllers.clear();
    for (var chapter in _currentNovel.chapters) {
      final controller = TextEditingController(text: chapter.content);
      controller.addListener(() {
        // 当文本发生变化时保存到历史记录
        if (_isEditing) {
          _saveCurrentToHistory();
        }
      });
      _chapterControllers.add(controller);
    }
  }

  void _saveCurrentToHistory() {
    if (_currentChapterIndex < _currentNovel.chapters.length) {
      // 只有当内容真正发生变化时才保存历史
      final currentChapter = _currentNovel.chapters[_currentChapterIndex];
      if (_undoHistory.isEmpty || _undoHistory.last != currentChapter.content) {
        _undoHistory.add(currentChapter.content);
        // 添加新的历史记录时清空重做历史
        _redoHistory.clear();
        setState(() {}); // 更新UI状态，使撤销按钮可用
      }
    }
  }

  void _undo() {
    if (_undoHistory.isNotEmpty) {
      // 保存当前状态到重做历史
      _redoHistory.add(_chapterControllers[_currentChapterIndex].text);
      // 恢复上一个状态
      final lastState = _undoHistory.removeLast();
      _chapterControllers[_currentChapterIndex].text = lastState;

      // 更新小说对象
      final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
      updatedChapters[_currentChapterIndex] =
          updatedChapters[_currentChapterIndex].copyWith(
        content: lastState,
      );
      _updateNovel(_currentNovel.copyWith(chapters: updatedChapters));
    }
  }

  void _redo() {
    if (_redoHistory.isNotEmpty) {
      // 保存当前状态到撤销历史
      _undoHistory.add(_chapterControllers[_currentChapterIndex].text);
      // 恢复下一个状态
      final nextState = _redoHistory.removeLast();
      _chapterControllers[_currentChapterIndex].text = nextState;

      // 更新小说对象
      final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
      updatedChapters[_currentChapterIndex] =
          updatedChapters[_currentChapterIndex].copyWith(
        content: nextState,
      );
      _updateNovel(_currentNovel.copyWith(chapters: updatedChapters));
    }
  }

  void _saveChanges() async {
    try {
      Novel finalNovel;

      if (_currentNovel.chapters.isEmpty) {
        // 短篇小说：直接保存内容
        finalNovel = _currentNovel.copyWith(
          title: _titleController.text,
        );
      } else {
        // 长篇小说：保存章节内容
        // 创建更新后的小说对象
        final updatedNovel = _currentNovel.copyWith(
          title: _titleController.text,
        );

        // 更新所有章节内容
        final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
        for (var i = 0; i < _currentNovel.chapters.length; i++) {
          updatedChapters[i] = updatedChapters[i].copyWith(
            content: _chapterControllers[i].text,
          );
        }

        // 更新小说内容
        finalNovel = updatedNovel.copyWith(
          chapters: updatedChapters,
          content: updatedChapters.map((c) => c.content).join('\n\n'),
        );
      }

      // 保存到数据库
      await _controller.saveNovel(finalNovel);

      // 更新本地状态
      _updateNovel(finalNovel);

      setState(() {
        _isEditing = false;
        // 清空历史记录
        _undoHistory.clear();
        _redoHistory.clear();
      });

      Get.snackbar(
        '保存成功',
        '所有修改已保存',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '发生错误：$e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        duration: const Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isEditing
            ? TextField(
                controller: _titleController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  hintText: '输入小说标题',
                  hintStyle: TextStyle(color: Colors.white70),
                ),
              )
            : Text(_currentNovel.title),
        actions: [
          if (_isEditing) ...[
            IconButton(
              icon: const Icon(Icons.undo),
              onPressed: _undoHistory.isEmpty ? null : _undo,
              tooltip: '撤销',
            ),
            IconButton(
              icon: const Icon(Icons.redo),
              onPressed: _redoHistory.isEmpty ? null : _redo,
              tooltip: '重做',
            ),
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveChanges,
              tooltip: '保存',
            ),
          ] else ...[
            // 非编辑模式下显示markdown渲染切换按钮
            IconButton(
              icon: Icon(_isMarkdownMode ? Icons.text_fields : Icons.code),
              onPressed: () {
                setState(() {
                  _isMarkdownMode = !_isMarkdownMode;
                });
              },
              tooltip: _isMarkdownMode ? '普通文本' : 'Markdown渲染',
            ),
          ],
          IconButton(
            icon: Icon(_isEditing ? Icons.close : Icons.edit),
            onPressed: () {
              if (_isEditing) {
                // 取消编辑，恢复原始内容
                _titleController.text = _currentNovel.title;
                _initChapterControllers();
                _undoHistory.clear();
                _redoHistory.clear();
              }
              setState(() {
                _isEditing = !_isEditing;
              });
            },
            tooltip: _isEditing ? '取消' : '编辑',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildChapterList(),
          Expanded(
            child: _buildChapterContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildChapterList() {
    // 如果是短篇小说（没有章节），不显示章节列表
    if (_currentNovel.chapters.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _currentNovel.chapters.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: ChoiceChip(
              label: Text('第${_currentNovel.chapters[index].number}章'),
              selected: _currentChapterIndex == index,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _currentChapterIndex = index;
                  });
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildChapterContent() {
    // 如果是短篇小说（没有章节），直接显示小说内容
    if (_currentNovel.chapters.isEmpty) {
      return _buildShortNovelContent();
    }

    if (_currentChapterIndex >= _currentNovel.chapters.length) {
      return const Center(child: Text('暂无内容'));
    }

    final chapter = _currentNovel.chapters[_currentChapterIndex];

    // 如果是第0章（大纲章节），使用专门的大纲查看器
    if (chapter.number == 0) {
      return _buildOutlineViewer(chapter);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            chapter.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _isEditing
              ? TextField(
                  controller: _chapterControllers[_currentChapterIndex],
                  maxLines: null,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '输入章节内容',
                  ),
                  contextMenuBuilder: (context, editableTextState) {
                    final List<ContextMenuButtonItem> buttonItems =
                        editableTextState.contextMenuButtonItems;

                    if (editableTextState.textEditingValue.selection.isValid &&
                        !editableTextState
                            .textEditingValue.selection.isCollapsed) {
                      _lastSelection =
                          editableTextState.textEditingValue.selection;
                      final selectedText = _lastSelection!.textInside(
                          _chapterControllers[_currentChapterIndex].text);

                      return AdaptiveTextSelectionToolbar(
                        anchors: editableTextState.contextMenuAnchors,
                        children: [
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () {
                              editableTextState.hideToolbar();
                              _showAIEditDialog(selectedText);
                            },
                            child: const Text(
                              'AI编辑',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                          const VerticalDivider(
                            width: 1,
                            indent: 8,
                            endIndent: 8,
                          ),
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () {
                              Clipboard.setData(
                                  ClipboardData(text: selectedText));
                              editableTextState.hideToolbar();
                            },
                            child: const Text(
                              '复制',
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                          const VerticalDivider(
                            width: 1,
                            indent: 8,
                            endIndent: 8,
                          ),
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () {
                              Clipboard.setData(
                                  ClipboardData(text: selectedText));
                              // 更新文本内容，删除选中部分
                              final text =
                                  _chapterControllers[_currentChapterIndex]
                                      .text;
                              final newText = text.replaceRange(
                                  _lastSelection!.start,
                                  _lastSelection!.end,
                                  '');
                              _chapterControllers[_currentChapterIndex].value =
                                  TextEditingValue(
                                text: newText,
                                selection: TextSelection.collapsed(
                                    offset: _lastSelection!.start),
                              );
                              editableTextState.hideToolbar();
                            },
                            child: const Text(
                              '剪切',
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                          const VerticalDivider(
                            width: 1,
                            indent: 8,
                            endIndent: 8,
                          ),
                          TextSelectionToolbarTextButton(
                            padding: const EdgeInsets.all(12.0),
                            onPressed: () async {
                              final data =
                                  await Clipboard.getData('text/plain');
                              if (data?.text != null) {
                                // 更新文本内容，插入剪贴板内容
                                final text =
                                    _chapterControllers[_currentChapterIndex]
                                        .text;
                                final newText = text.replaceRange(
                                    _lastSelection!.start,
                                    _lastSelection!.end,
                                    data!.text!);
                                _chapterControllers[_currentChapterIndex]
                                    .value = TextEditingValue(
                                  text: newText,
                                  selection: TextSelection.collapsed(
                                      offset: _lastSelection!.start +
                                          data.text!.length),
                                );
                              }
                              editableTextState.hideToolbar();
                            },
                            child: const Text(
                              '粘贴',
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      );
                    }

                    return AdaptiveTextSelectionToolbar.buttonItems(
                      anchors: editableTextState.contextMenuAnchors,
                      buttonItems: buttonItems,
                    );
                  },
                )
              : _isMarkdownMode
                  ? MarkdownBody(
                      data: chapter.content,
                      selectable: true,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          fontSize: 16,
                          height: 1.8,
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.w400,
                        ),
                        h1: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h2: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h3: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        blockquote: TextStyle(
                          fontSize: 16,
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                        ),
                        code: TextStyle(
                          fontSize: 14,
                          fontFamily: 'monospace',
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    )
                  : SelectableText(
                      chapter.content,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.8,
                        // 使用主题的onSurface颜色，确保在深色模式下有足够的对比度
                        color: Theme.of(context).colorScheme.onSurface,
                        // 增加字体粗细，提高可读性
                        fontWeight: FontWeight.w400,
                      ),
                      onSelectionChanged: (selection, cause) {
                        if (!selection.isCollapsed) {
                          _lastSelection = selection;
                        }
                      },
                      contextMenuBuilder: (context, editableTextState) {
                        if (editableTextState.textEditingValue.selection.isValid &&
                            !editableTextState
                                .textEditingValue.selection.isCollapsed) {
                          final selectedText = editableTextState
                              .textEditingValue.selection
                              .textInside(chapter.content);

                          return AdaptiveTextSelectionToolbar(
                            anchors: editableTextState.contextMenuAnchors,
                            children: [
                              TextSelectionToolbarTextButton(
                                padding: const EdgeInsets.all(12.0),
                                onPressed: () {
                                  editableTextState.hideToolbar();
                                  _showAIEditDialog(selectedText);
                                },
                                child: const Text(
                                  'AI编辑',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                              const VerticalDivider(
                                width: 1,
                                indent: 8,
                                endIndent: 8,
                              ),
                              TextSelectionToolbarTextButton(
                                padding: const EdgeInsets.all(12.0),
                                onPressed: () {
                                  Clipboard.setData(
                                      ClipboardData(text: selectedText));
                                  editableTextState.hideToolbar();
                                },
                                child: const Text(
                                  '复制',
                                  style: TextStyle(fontSize: 14),
                                ),
                              ),
                            ],
                          );
                        }

                        return AdaptiveTextSelectionToolbar.buttonItems(
                          anchors: editableTextState.contextMenuAnchors,
                          buttonItems: editableTextState.contextMenuButtonItems,
                        );
                      },
                    ),
        ],
      ),
    );
  }

  // 构建短篇小说内容显示
  Widget _buildShortNovelContent() {
    final TextEditingController contentController =
        TextEditingController(text: _currentNovel.content);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _currentNovel.title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _isEditing
              ? TextField(
                  controller: contentController,
                  maxLines: null,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '输入小说内容',
                  ),
                  onChanged: (value) {
                    // 实时更新小说内容
                    _currentNovel = _currentNovel.copyWith(content: value);
                  },
                )
              : _isMarkdownMode
                  ? MarkdownBody(
                      data: _currentNovel.content.isEmpty
                          ? '暂无内容'
                          : _currentNovel.content,
                      selectable: true,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          fontSize: 16,
                          height: 1.8,
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.w400,
                        ),
                        h1: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h2: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h3: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        blockquote: TextStyle(
                          fontSize: 16,
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                        ),
                        code: TextStyle(
                          fontSize: 14,
                          fontFamily: 'monospace',
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    )
                  : SelectableText(
                      _currentNovel.content.isEmpty
                          ? '暂无内容'
                          : _currentNovel.content,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.8,
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    for (var controller in _chapterControllers) {
      controller.dispose();
    }
    _outlineScrollController.dispose();
    super.dispose();
  }

  void _updateNovel(Novel newNovel) {
    setState(() {
      _currentNovel = newNovel;
    });
  }

  Future<void> _showAIEditDialog(String selectedText) async {
    final promptController = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI编辑文本'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('已选中文本：',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.3,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  padding: const EdgeInsets.all(8),
                  child: SingleChildScrollView(
                    child: Text(
                      selectedText,
                      style: TextStyle(
                        // 使用主题的onSurface颜色，确保在深色模式下有足够的对比度
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: promptController,
                  decoration: const InputDecoration(
                    labelText: '修改指令',
                    hintText: '例如：改写成更生动的描述',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(promptController.text),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      _modifyTextWithAI(selectedText, result);
    }
  }

  Future<void> _modifyTextWithAI(String originalText, String prompt) async {
    final modifiedTextController = TextEditingController();

    try {
      Get.dialog(
        AlertDialog(
          title: const Text('AI正在修改文本'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const LinearProgressIndicator(),
                  const SizedBox(height: 16),
                  TextField(
                    controller: modifiedTextController,
                    maxLines: null,
                    readOnly: true,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.all(8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('取消'),
            ),
          ],
        ),
        barrierDismissible: false,
      );

      final systemPrompt = '''请根据用户的要求修改以下文本。要求：
1. 保持修改后的文本与上下文的连贯性
2. 保持人物、场景等设定的一致性
3. 确保修改后的文本符合整体风格
4. 避免出现与原意相违背的内容

原文：
$originalText

用户要求：
$prompt''';

      const userPrompt = '请按照上述要求修改文本，直接输出修改后的内容，不需要其他解释。';

      String modifiedText = '';
      await for (final chunk in _aiService.generateTextStream(
        systemPrompt: systemPrompt,
        userPrompt: userPrompt,
        maxTokens: 2000,
        temperature: 0.7,
      )) {
        modifiedText += chunk;
        modifiedTextController.text = modifiedText;
      }

      Get.back(); // 关闭生成对话框

      if (_lastSelection != null) {
        if (_isEditing) {
          // 编辑模式下直接更新文本控制器
          final text = _chapterControllers[_currentChapterIndex].text;
          final start = _lastSelection!.start;
          final end = _lastSelection!.end;

          if (start >= 0 && end <= text.length && start <= end) {
            final newText = text.replaceRange(start, end, modifiedText.trim());
            _chapterControllers[_currentChapterIndex].value = TextEditingValue(
              text: newText,
              selection: TextSelection.collapsed(
                  offset: start + modifiedText.trim().length),
            );

            // 标记有更改，但不立即保存
            setState(() {
              // 保存当前状态到历史记录
              _saveCurrentToHistory();
            });
          }
        } else {
          // 非编辑模式下更新章节内容
          final chapter = _currentNovel.chapters[_currentChapterIndex];
          final originalContent = chapter.content;
          final start = _lastSelection!.start;
          final end = _lastSelection!.end;

          if (start >= 0 && end <= originalContent.length && start <= end) {
            final newContent =
                originalContent.replaceRange(start, end, modifiedText.trim());

            // 创建更新后的章节
            final updatedChapter = chapter.copyWith(content: newContent);

            // 更新小说对象
            final updatedChapters = List<Chapter>.from(_currentNovel.chapters);
            updatedChapters[_currentChapterIndex] = updatedChapter;

            // 更新小说
            final updatedNovel = _currentNovel.copyWith(
              chapters: updatedChapters,
              content: updatedChapters.map((c) => c.content).join('\n\n'),
            );

            // 保存到数据库
            _controller.saveNovel(updatedNovel);

            // 更新本地状态
            _updateNovel(updatedNovel);

            // 更新编辑器控制器，以防后续切换到编辑模式
            if (_currentChapterIndex < _chapterControllers.length) {
              _chapterControllers[_currentChapterIndex].text = newContent;
            }
          }
        }

        Get.snackbar(
          '修改完成',
          '文本已更新',
          backgroundColor: Colors.green.withAlpha(25),
          duration: const Duration(seconds: 2),
        );
      }
    } catch (e) {
      // 确保对话框已关闭
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        '修改失败',
        e.toString(),
        backgroundColor: Colors.red.withAlpha(25),
        duration: const Duration(seconds: 3),
      );
    } finally {
      // 确保资源被释放
      modifiedTextController.dispose();
    }
  }
}

class NovelDetailController extends GetxController {
  final Novel novel;
  final RxList<int> selectedChapters = <int>[].obs;
  final RxBool isReviewing = false.obs;
  final RxBool isGenerating = false.obs;
  final RxBool isPaused = false.obs;
  final RxInt currentProcessingChapter = 0.obs;
  final reviewRequirementsController = TextEditingController();
  final _contentReviewService = Get.find<ContentReviewService>();
  final _aiService = Get.find<AIService>();

  NovelDetailController(this.novel);

  @override
  void onInit() {
    super.onInit();
    checkForUnfinishedTask();
  }

  @override
  void onClose() {
    reviewRequirementsController.dispose();
    super.onClose();
  }

  void updateGeneratingStatus(bool value) {
    isGenerating.value = value;
  }

  void updatePausedStatus(bool value) {
    isPaused.value = value;
  }

  Future<void> checkForUnfinishedTask() async {
    final prefs = await SharedPreferences.getInstance();
    final savedTask = prefs.getString('unfinished_task_${novel.id}');

    if (savedTask != null) {
      final taskData = json.decode(savedTask);
      selectedChapters.value = List<int>.from(taskData['selected_chapters']);
      currentProcessingChapter.value = taskData['current_chapter'];

      if (selectedChapters.isNotEmpty) {
        Get.dialog(
          AlertDialog(
            title: const Text('发现未完成的任务'),
            content: const Text('是否继续上次未完成的润色任务？'),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                  clearUnfinishedTask();
                },
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () {
                  Get.back();
                  resumeUnfinishedTask(taskData['requirements']);
                },
                child: const Text('继续'),
              ),
            ],
          ),
        );
      }
    }
  }

  Future<void> clearUnfinishedTask() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('unfinished_task_${novel.id}');
    selectedChapters.clear();
    currentProcessingChapter.value = 0;
    isPaused.value = false;
    isGenerating.value = false;
  }

  Future<void> saveCurrentProgress() async {
    if (!isGenerating.value) return;

    final prefs = await SharedPreferences.getInstance();
    final taskData = {
      'selected_chapters': selectedChapters.toList(),
      'current_chapter': currentProcessingChapter.value,
      'requirements': reviewRequirementsController.text,
    };
    await prefs.setString('unfinished_task_${novel.id}', json.encode(taskData));
  }

  void pauseGeneration() {
    isPaused.value = true;
    saveCurrentProgress();
  }

  void resumeGeneration() {
    isPaused.value = false;
    continueGeneration();
  }

  Future<void> resumeUnfinishedTask(String requirements) async {
    reviewRequirementsController.text = requirements;
    isGenerating.value = true;
    await continueGeneration();
  }

  Future<void> continueGeneration() async {
    if (!isGenerating.value) return;

    try {
      for (int i = currentProcessingChapter.value;
          i < selectedChapters.length;
          i++) {
        if (isPaused.value) {
          await saveCurrentProgress();
          return;
        }

        currentProcessingChapter.value = i;
        final chapterIndex = selectedChapters[i];
        final chapter = novel.chapters[chapterIndex];

        final reviewedContent = await _contentReviewService.reviewContent(
          content: chapter.content,
          style: '与原文风格一致',
          model: AIModel.values.firstWhere(
            (m) =>
                m.toString().split('.').last ==
                Get.find<ApiConfigController>().selectedModelId.value,
            orElse: () => AIModel.deepseek,
          ),
        );

        novel.chapters[chapterIndex] =
            chapter.copyWith(content: reviewedContent);
        await saveCurrentProgress();
      }

      Get.snackbar('成功', '章节润色完成');
      await clearUnfinishedTask();
    } catch (e) {
      Get.snackbar('错误', '章节润色失败：$e');
      isPaused.value = true;
      await saveCurrentProgress();
    }
  }

  Future<void> reviewSelectedChapters() async {
    try {
      isGenerating.value = true;
      isPaused.value = false;
      currentProcessingChapter.value = 0;
      Get.back(); // 关闭对话框

      await continueGeneration();
    } catch (e) {
      Get.snackbar('错误', '章节润色失败：$e');
    }
  }

  void showReviewDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('章节润色'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() => Text('已选择 ${selectedChapters.length} 个章节')),
            const SizedBox(height: 16),
            TextField(
              controller: reviewRequirementsController,
              decoration: const InputDecoration(
                labelText: '润色要求（可选）',
                hintText: '请输入具体的润色要求...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: reviewSelectedChapters,
            child: const Text('开始润色'),
          ),
        ],
      ),
    );
  }

  Future<void> generateContent() async {
    if (isGenerating.value) return;

    try {
      updateGeneratingStatus(true);

      await for (final _ in _aiService.generateTextStream(
        systemPrompt: '''作为一个专业的小说创作助手，请遵循以下创作原则：

1. 故事逻辑：
   - 确保因果关系清晰合理，事件发展有其必然性
   - 人物行为要符合其性格特征和处境
   - 情节转折要有铺垫，避免突兀
   - 矛盾冲突的解决要符合逻辑
   - 故事背景要前后一致，细节要互相呼应

2. 叙事结构：
   - 采用灵活多变的叙事手法，避免单一直线式发展
   - 合理安排伏笔和悬念，让故事更有层次感
   - 注意时间线的合理性，避免前后矛盾
   - 场景转换要流畅自然，不生硬突兀
   - 故事节奏要有张弛，紧凑处突出戏剧性

3. 人物塑造：
   - 赋予角色丰富的心理活动和独特性格
   - 人物成长要符合其经历和环境
   - 人物关系要复杂立体，互动要自然
   - 对话要体现人物性格和身份特点
   - 避免脸谱化和类型化的人物描写

4. 环境描写：
   - 场景描写要与情节和人物情感相呼应
   - 细节要生动传神，突出关键特征
   - 环境氛围要配合故事发展
   - 感官描写要丰富多样
   - 避免无关的环境描写，保持紧凑

5. 语言表达：
   - 用词准确生动，避免重复和陈词滥调
   - 句式灵活多样，富有韵律感
   - 善用修辞手法，但不过分堆砌
   - 对话要自然流畅，符合说话人特点
   - 描写要细腻传神，避免空洞''',
        userPrompt: reviewRequirementsController.text,
        maxTokens: 7000,
        temperature: 0.7,
      )) {
        // 这里可以处理每个生成的文本块
      }

      // 生成完成后的处理
    } catch (e) {
      Get.snackbar('错误', '生成失败：$e');
    } finally {
      updateGeneratingStatus(false);
    }
  }
}

class ChapterDetailScreen extends StatelessWidget {
  final Chapter chapter;

  const ChapterDetailScreen({
    super.key,
    required this.chapter,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('第${chapter.number}章：${chapter.title}'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          chapter.content,
          style: const TextStyle(fontSize: 16.0, height: 1.6),
        ),
      ),
    );
  }

  /// 构建大纲查看器，支持分块显示和索引
  Widget _buildOutlineViewer(Chapter outlineChapter) {
    final outlineContent = outlineChapter.content;
    final outlineSections = _parseOutlineSections(outlineContent);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧索引栏
        Container(
          width: 200,
          height: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
          ),
          child: _buildOutlineIndex(outlineSections),
        ),
        // 右侧内容区域
        Expanded(
          child: _buildOutlineContent(outlineSections),
        ),
      ],
    );
  }

  /// 解析大纲内容为分块结构
  List<OutlineSection> _parseOutlineSections(String content) {
    final sections = <OutlineSection>[];
    final lines = content.split('\n');

    OutlineSection? currentSection;
    final contentBuffer = StringBuffer();

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final trimmedLine = line.trim();

      if (trimmedLine.isEmpty) {
        contentBuffer.writeln(line);
        continue;
      }

      // 检测章节标题（第X章：标题 或 第X章 标题）
      final chapterMatch = RegExp(r'^第(\d+)章[：:\s]*(.*)$').firstMatch(trimmedLine);
      if (chapterMatch != null) {
        // 保存前一个章节
        if (currentSection != null) {
          currentSection.content = contentBuffer.toString().trim();
          if (currentSection.content.isNotEmpty) {
            sections.add(currentSection);
          }
          contentBuffer.clear();
        }

        // 创建新章节
        final chapterNumber = int.tryParse(chapterMatch.group(1)!) ?? 0;
        final chapterTitle = chapterMatch.group(2)?.trim() ?? '';
        final displayTitle = chapterTitle.isNotEmpty
          ? '第$chapterNumber章：$chapterTitle'
          : '第$chapterNumber章';

        currentSection = OutlineSection(
          id: 'chapter_$chapterNumber',
          title: displayTitle,
          type: OutlineSectionType.chapter,
          content: '',
        );
        continue;
      }

      // 检测主要标题（# 标题）
      final h1Match = RegExp(r'^#+\s+(.+)$').firstMatch(trimmedLine);
      if (h1Match != null) {
        // 保存前一个部分
        if (currentSection != null) {
          currentSection.content = contentBuffer.toString().trim();
          if (currentSection.content.isNotEmpty) {
            sections.add(currentSection);
          }
          contentBuffer.clear();
        }

        // 创建新部分
        final title = h1Match.group(1)!.trim();
        final level = trimmedLine.indexOf(' ');
        final type = level <= 2 ? OutlineSectionType.section : OutlineSectionType.subsection;

        currentSection = OutlineSection(
          id: 'section_${sections.length}',
          title: title,
          type: type,
          content: '',
        );
        continue;
      }

      // 检测特殊格式的标题（如：小说标题：、生成时间：等）
      final specialMatch = RegExp(r'^([^：:]+)[：:](.+)$').firstMatch(trimmedLine);
      if (specialMatch != null) {
        final key = specialMatch.group(1)!.trim();
        final value = specialMatch.group(2)!.trim();

        // 如果是重要的元信息，创建单独的部分
        if (['小说标题', '生成时间', '总章节数', '世界观设定', '详细大纲'].contains(key)) {
          // 保存前一个部分
          if (currentSection != null) {
            currentSection.content = contentBuffer.toString().trim();
            if (currentSection.content.isNotEmpty) {
              sections.add(currentSection);
            }
            contentBuffer.clear();
          }

          currentSection = OutlineSection(
            id: 'meta_${sections.length}',
            title: key,
            type: OutlineSectionType.subsection,
            content: value,
          );
          continue;
        }
      }

      // 添加内容行
      contentBuffer.writeln(line);
    }

    // 保存最后一个部分
    if (currentSection != null) {
      currentSection.content = contentBuffer.toString().trim();
      if (currentSection.content.isNotEmpty) {
        sections.add(currentSection);
      }
    }

    // 如果没有找到任何结构化内容，创建一个默认部分
    if (sections.isEmpty) {
      sections.add(OutlineSection(
        id: 'default',
        title: '大纲内容',
        type: OutlineSectionType.section,
        content: content,
      ));
    }

    return sections;
  }

  /// 构建左侧索引栏
  Widget _buildOutlineIndex(List<OutlineSection> sections) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: sections.length,
      itemBuilder: (context, index) {
        final section = sections[index];
        return _buildIndexItem(section, index);
      },
    );
  }

  /// 构建索引项
  Widget _buildIndexItem(OutlineSection section, int index) {
    final isSelected = _selectedOutlineSectionIndex == index;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Material(
        color: isSelected
          ? Theme.of(context).colorScheme.primaryContainer
          : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _selectedOutlineSectionIndex = index;
            });
            // 滚动到对应内容
            _scrollToSection(index);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                // 类型图标
                Icon(
                  _getSectionIcon(section.type),
                  size: 16,
                  color: isSelected
                    ? Theme.of(context).colorScheme.onPrimaryContainer
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                // 标题
                Expanded(
                  child: Text(
                    section.title,
                    style: TextStyle(
                      fontSize: _getSectionFontSize(section.type),
                      fontWeight: _getSectionFontWeight(section.type),
                      color: isSelected
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取章节类型对应的图标
  IconData _getSectionIcon(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return Icons.book;
      case OutlineSectionType.section:
        return Icons.folder;
      case OutlineSectionType.subsection:
        return Icons.description;
    }
  }

  /// 获取章节类型对应的字体大小
  double _getSectionFontSize(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return 14;
      case OutlineSectionType.section:
        return 13;
      case OutlineSectionType.subsection:
        return 12;
    }
  }

  /// 获取章节类型对应的字体粗细
  FontWeight _getSectionFontWeight(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return FontWeight.bold;
      case OutlineSectionType.section:
        return FontWeight.w600;
      case OutlineSectionType.subsection:
        return FontWeight.w500;
    }
  }

  /// 滚动到指定章节
  void _scrollToSection(int index) {
    if (_outlineScrollController.hasClients) {
      // 计算滚动位置（每个章节大约占用的高度）
      final position = index * 200.0;
      _outlineScrollController.animateTo(
        position,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 构建右侧内容区域
  Widget _buildOutlineContent(List<OutlineSection> sections) {
    return SingleChildScrollView(
      controller: _outlineScrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: sections.asMap().entries.map((entry) {
          final index = entry.key;
          final section = entry.value;
          return _buildSectionContent(section, index);
        }).toList(),
      ),
    );
  }

  /// 构建单个章节内容
  Widget _buildSectionContent(OutlineSection section, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 章节标题
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: _getSectionBackgroundColor(section.type),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getSectionIcon(section.type),
                  size: 20,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    section.title,
                    style: TextStyle(
                      fontSize: _getSectionContentFontSize(section.type),
                      fontWeight: _getSectionFontWeight(section.type),
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // 章节内容
          if (section.content.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerLowest,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
              child: _isMarkdownMode
                  ? MarkdownBody(
                      data: section.content,
                      selectable: true,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          fontSize: 14,
                          height: 1.6,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h1: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h2: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        h3: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    )
                  : SelectableText(
                      section.content,
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.6,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
            ),
        ],
      ),
    );
  }

  /// 获取章节类型对应的背景色
  Color _getSectionBackgroundColor(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3);
      case OutlineSectionType.section:
        return Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3);
      case OutlineSectionType.subsection:
        return Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3);
    }
  }

  /// 获取章节内容对应的字体大小
  double _getSectionContentFontSize(OutlineSectionType type) {
    switch (type) {
      case OutlineSectionType.chapter:
        return 18;
      case OutlineSectionType.section:
        return 16;
      case OutlineSectionType.subsection:
        return 14;
    }
  }
}

/// 大纲章节数据类
class OutlineSection {
  final String id;
  final String title;
  final OutlineSectionType type;
  String content;

  OutlineSection({
    required this.id,
    required this.title,
    required this.type,
    required this.content,
  });
}

/// 大纲章节类型枚举
enum OutlineSectionType {
  chapter,    // 章节
  section,    // 主要部分
  subsection, // 子部分
}
